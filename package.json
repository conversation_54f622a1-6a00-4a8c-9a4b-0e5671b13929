{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "AI-powered document processing and workflow automation platform", "main": "backend/server.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ai\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:ai": "cd ai-engine && python main.py", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "test:watch": "npm run test:backend -- --watch", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:backend -- --fix && npm run lint:frontend -- --fix", "db:migrate": "cd backend && npx prisma migrate dev", "db:seed": "cd backend && npx prisma db seed", "db:studio": "cd backend && npx prisma studio", "db:reset": "cd backend && npx prisma migrate reset", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm install && cd backend && npm install && cd ../frontend && npm install && cd ../ai-engine && pip install -r requirements.txt", "clean": "rm -rf node_modules backend/node_modules frontend/node_modules frontend/.next backend/dist", "postinstall": "husky install"}, "keywords": ["document-processing", "workflow-automation", "ai", "ocr", "contract-management", "invoice-processing", "knowledge-management"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/ProcessGenius.git"}, "bugs": {"url": "https://github.com/HectorTa1989/ProcessGenius/issues"}, "homepage": "https://github.com/HectorTa1989/ProcessGenius#readme", "devDependencies": {"concurrently": "^7.6.0", "husky": "^8.0.3", "lint-staged": "^13.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}