{"name": "processgenius-backend", "version": "1.0.0", "description": "Backend API for ProcessGenius document processing platform", "main": "server.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "build": "echo 'No build step required for Node.js'", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:reset": "npx prisma migrate reset"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^6.1.5", "morgan": "^1.10.0", "dotenv": "^16.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "joi": "^17.9.1", "multer": "^1.4.5-lts.1", "sharp": "^0.32.1", "tesseract.js": "^4.0.2", "pdf-parse": "^1.1.1", "mammoth": "^1.5.1", "node-cron": "^3.0.2", "nodemailer": "^6.9.1", "winston": "^3.8.2", "express-rate-limit": "^6.7.0", "express-validator": "^6.15.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "ioredis": "^5.3.2", "bull": "^4.10.4", "axios": "^1.4.0", "form-data": "^4.0.0", "uuid": "^9.0.0", "moment": "^2.29.4", "lodash": "^4.17.21", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "archiver": "^5.3.1", "socket.io": "^4.6.2", "prisma": "^4.15.0", "@prisma/client": "^4.15.0", "openai": "^3.2.1", "pinecone-client": "^1.1.0", "google-auth-library": "^8.8.0", "googleapis": "^118.0.0", "dropbox": "^10.34.0"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3", "eslint": "^8.40.0", "eslint-config-node": "^4.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^2.8.8", "@types/jest": "^29.5.1"}, "prisma": {"seed": "node prisma/seed.js"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!**/tests/**"], "testMatch": ["**/tests/**/*.test.js"]}, "keywords": ["api", "backend", "document-processing", "express", "nodejs"], "author": "HectorTa1989", "license": "MIT"}